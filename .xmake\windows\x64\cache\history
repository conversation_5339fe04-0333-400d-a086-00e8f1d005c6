{
    cmdlines = {
        [[xmake l c:\Users\<USER>\.void-editor\extensions\tboox.xmake-vscode-2.4.0\assets\config.lua]],
        [[xmake l c:\Users\<USER>\.void-editor\extensions\tboox.xmake-vscode-2.4.0\assets\update_intellisense.lua .vscode clangd]],
        [[xmake l c:\Users\<USER>\.void-editor\extensions\tboox.xmake-vscode-2.4.0\assets\config.lua]],
        "xmake check",
        [[xmake l c:\Users\<USER>\.void-editor\extensions\tboox.xmake-vscode-2.4.0\assets\explorer.lua]],
        "xmake build",
        "xmake f -p windows -a x64 -m release",
        "xmake ",
        "xmake build",
        "xmake ",
        "xmake ",
        "xmake ",
        "xmake build",
        "xmake build",
        "xmake ",
        "xmake clean",
        "xmake ",
        "xmake ",
        "xmake clean",
        "xmake ",
        [[xmake l c:\Users\<USER>\.void-editor\extensions\tboox.xmake-vscode-2.4.0\assets\explorer.lua]],
        [[xmake l c:\Users\<USER>\.void-editor\extensions\tboox.xmake-vscode-2.4.0\assets\config.lua]]
    }
}