{
    find_program_mingw_arch_x64_plat_windows_checktoolld = {
        ["x86_64-w64-mingw32-g++"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]]
    },
    find_program_mingw_arch_x64_plat_windows_checktoolcc = {
        ["x86_64-w64-mingw32-gcc"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-gcc]]
    },
    find_program_mingw_arch_x64_plat_windows_checktoolsh = {
        ["x86_64-w64-mingw32-g++"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]]
    },
    find_program = {
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]]
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0"] = {
            ["--ignore-unresolved-symbol"] = true,
            ["--remap-inputs"] = true,
            ["-u"] = true,
            ["-c"] = true,
            ["--no-map-whole-files"] = true,
            ["-m"] = true,
            ["--no-gc-sections"] = true,
            ["-no-pie"] = true,
            ["-EB"] = true,
            ["--map-whole-files"] = true,
            ["--no-relax"] = true,
            ["--push-state"] = true,
            ["--section-start"] = true,
            ["--copy-dt-needed-entries"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--export-dynamic"] = true,
            ["--gpsize"] = true,
            ["--print-map-locals"] = true,
            ["--no-print-gc-sections"] = true,
            ["--split-by-file"] = true,
            ["--trace-symbol"] = true,
            ["--pic-executable"] = true,
            ["-Qy"] = true,
            ["--major-image-version"] = true,
            ["--format"] = true,
            ["--undefined"] = true,
            ["--omagic"] = true,
            ["--no-keep-memory"] = true,
            ["--enable-linker-version"] = true,
            ["--no-fatal-warnings"] = true,
            ["--architecture"] = true,
            ["--error-unresolved-symbols"] = true,
            ["--no-print-map-locals"] = true,
            ["-L"] = true,
            ["--export-dynamic-symbol-list"] = true,
            ["--auxiliary"] = true,
            ["-EL"] = true,
            ["--no-allow-shlib-undefined"] = true,
            ["--no-print-map-discarded"] = true,
            ["--no-whole-archive"] = true,
            ["-dp"] = true,
            ["--no-accept-unknown-input-arch"] = true,
            ["--disable-auto-image-base"] = true,
            ["--warn-common"] = true,
            ["-T"] = true,
            ["--disable-reloc-section"] = true,
            ["-Ttext"] = true,
            ["-e"] = true,
            ["--enable-non-contiguous-regions"] = true,
            ["--allow-shlib-undefined"] = true,
            ["--error-handling-script"] = true,
            ["--remap-inputs-file"] = true,
            ["--out-implib"] = true,
            ["--minor-subsystem-version"] = true,
            ["-R"] = true,
            ["--strip-all"] = true,
            ["-Map"] = true,
            ["--entry"] = true,
            ["--no-warn-rwx-segments"] = true,
            ["--relax"] = true,
            ["--minor-os-version"] = true,
            ["--heap"] = true,
            ["-debug"] = true,
            ["--disable-multiple-abs-defs"] = true,
            ["--stats"] = true,
            ["-Bsymbolic"] = true,
            ["--sort-section"] = true,
            ["--nmagic"] = true,
            ["--dependency-file"] = true,
            ["-assert"] = true,
            ["--no-as-needed"] = true,
            ["--print-gc-sections"] = true,
            ["--no-dynamic-linker"] = true,
            ["--start-group"] = true,
            ["--minor-image-version"] = true,
            ["--strip-discarded"] = true,
            ["--just-symbols"] = true,
            ["--task-link"] = true,
            ["--trace"] = true,
            ["-f"] = true,
            ["--emit-relocs"] = true,
            ["--force-exe-suffix"] = true,
            ["--discard-locals"] = true,
            ["--warn-alternate-em"] = true,
            ["--version-script"] = true,
            ["--enable-long-section-names"] = true,
            ["-Ur"] = true,
            ["-flto"] = true,
            ["--enable-auto-image-base"] = true,
            ["--enable-extra-pe-debug"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--subsystem"] = true,
            ["--defsym"] = true,
            ["--default-image-base-high"] = true,
            ["--verbose"] = true,
            ["--end-group"] = true,
            ["--gc-keep-exported"] = true,
            ["--undefined-version"] = true,
            ["--require-defined"] = true,
            ["-V"] = true,
            ["-soname"] = true,
            ["--warn-unresolved-symbols"] = true,
            ["--large-address-aware"] = true,
            ["-plugin-save-temps"] = true,
            ["--discard-all"] = true,
            ["-fini"] = true,
            ["--whole-archive"] = true,
            ["--no-define-common"] = true,
            ["-g"] = true,
            ["--print-output-format"] = true,
            ["--dynamic-list"] = true,
            ["--script"] = true,
            ["--discard-none"] = true,
            ["-h"] = true,
            ["--export-all-symbols"] = true,
            ["--accept-unknown-input-arch"] = true,
            ["--default-imported-symver"] = true,
            ["-Bsymbolic-functions"] = true,
            ["--support-old-code"] = true,
            ["-l"] = true,
            ["--dynamic-linker"] = true,
            ["-dT"] = true,
            ["-O"] = true,
            ["-A"] = true,
            ["--mri-script"] = true,
            ["--enable-extra-pep-debug"] = true,
            ["--library-path"] = true,
            ["--version-exports-section"] = true,
            ["-plugin-opt"] = true,
            ["--as-needed"] = true,
            ["--warn-duplicate-exports"] = true,
            ["-Bno-symbolic"] = true,
            ["-G"] = true,
            ["--enable-reloc-section"] = true,
            ["--no-export-dynamic"] = true,
            ["-y"] = true,
            ["--reduce-memory-overheads"] = true,
            ["--filter"] = true,
            ["--warn-multiple-gp"] = true,
            ["--output"] = true,
            ["--traditional-format"] = true,
            ["--target-help"] = true,
            ["-I"] = true,
            ["--print-memory-usage"] = true,
            ["--no-warnings"] = true,
            ["-Tbss"] = true,
            ["--no-warn-search-mismatch"] = true,
            ["--disable-linker-version"] = true,
            ["--disable-large-address-aware"] = true,
            ["--image-base"] = true,
            ["-rpath"] = true,
            ["-Trodata-segment"] = true,
            ["-qmagic"] = true,
            ["--retain-symbols-file"] = true,
            ["--print-map-discarded"] = true,
            ["--dynamic-list-data"] = true,
            ["--default-symver"] = true,
            ["--no-copy-dt-needed-entries"] = true,
            ["--stack"] = true,
            ["--library"] = true,
            ["--help"] = true,
            ["--no-check-sections"] = true,
            ["-Tldata-segment"] = true,
            ["--ctf-variables"] = true,
            ["--spare-dynamic-tags"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--allow-multiple-definition"] = true,
            ["--pop-state"] = true,
            ["--gc-sections"] = true,
            ["-a"] = true,
            ["--strip-debug"] = true,
            ["--print-sysroot"] = true,
            ["--add-stdcall-alias"] = true,
            ["--section-ordering-file"] = true,
            ["--split-by-reloc"] = true,
            ["-Y"] = true,
            ["-rpath-link"] = true,
            ["--sort-common"] = true,
            ["-o"] = true,
            ["--no-strip-discarded"] = true,
            ["--exclude-all-symbols"] = true,
            ["--output-def"] = true,
            ["-F"] = true,
            ["--disable-auto-import"] = true,
            ["--orphan-handling"] = true,
            ["-init"] = true,
            ["-nostdlib"] = true,
            ["--disable-long-section-names"] = true,
            ["--no-undefined-version"] = true,
            ["--compat-implib"] = true,
            ["--cref"] = true,
            ["--wrap"] = true,
            ["--relocatable"] = true,
            ["--default-image-base-low"] = true,
            ["-Tdata"] = true,
            ["--dynamic-list-cpp-new"] = true,
            ["--demangle"] = true,
            ["--dynamic-list-cpp-typeinfo"] = true,
            ["--version"] = true,
            ["-plugin"] = true,
            ["-b"] = true,
            ["--warn-section-align"] = true,
            ["-Ttext-segment"] = true,
            ["--warn-once"] = true,
            ["--file-alignment"] = true,
            ["--no-omagic"] = true,
            ["--section-alignment"] = true,
            ["--fatal-warnings"] = true,
            ["--no-undefined"] = true,
            ["-static"] = true,
            ["--force-group-allocation"] = true,
            ["--enable-auto-import"] = true,
            ["--print-map"] = true,
            ["--default-script"] = true,
            ["--exclude-modules-for-implib"] = true,
            ["--exclude-libs"] = true,
            ["--enable-non-contiguous-regions-warnings"] = true,
            ["-Bshareable"] = true,
            ["--kill-at"] = true,
            ["--oformat"] = true,
            ["--no-demangle"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--dll"] = true,
            ["--no-warn-mismatch"] = true,
            ["--exclude-symbols"] = true,
            ["--check-sections"] = true,
            ["--warn-textrel"] = true,
            ["--export-dynamic-symbol"] = true,
            ["--no-ctf-variables"] = true,
            ["--major-os-version"] = true,
            ["--major-subsystem-version"] = true,
            ["--unique"] = true
        }
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0"] = {
            ["-S"] = true,
            ["-x"] = true,
            ["-print-multiarch"] = true,
            ["-v"] = true,
            ["-dumpmachine"] = true,
            ["--target-help"] = true,
            ["--version"] = true,
            ["-c"] = true,
            ["-Xassembler"] = true,
            ["-print-search-dirs"] = true,
            ["-o"] = true,
            ["-save-temps"] = true,
            ["-Xpreprocessor"] = true,
            ["--help"] = true,
            ["-print-multi-directory"] = true,
            ["-E"] = true,
            ["-no-canonical-prefixes"] = true,
            ["--param"] = true,
            ["-print-multi-os-directory"] = true,
            ["-shared"] = true,
            ["-pipe"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-Xlinker"] = true,
            ["-dumpspecs"] = true,
            ["-B"] = true,
            ["-print-sysroot-headers-suffix"] = true,
            ["-pass-exit-codes"] = true,
            ["-print-sysroot"] = true,
            ["-time"] = true,
            ["-pie"] = true,
            ["-print-multi-lib"] = true,
            ["-dumpversion"] = true
        }
    },
    ["lib.detect.has_flags"] = {
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-Wno-gnu-line-marker -Werror"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_ld_ldflags__-flto"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-fdiagnostics-color=always"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-march=native"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-ftree-vectorize"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-funroll-loops"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-Os"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-fomit-frame-pointer"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-fdata-sections"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-ffast-math"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_ld_ldflags__-Wl,--gc-sections"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-ffunction-sections"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-O3"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-MMD -MF"] = true
    },
    ["detect.sdks.find_mingw"] = {
        mingw = {
            sdkdir = [[C:\msys64\mingw64]],
            cross = "x86_64-w64-mingw32-",
            bindir = [[C:\msys64\mingw64\bin]]
        }
    },
    find_program_mingw_arch_x64_plat_windows_checktoolcxx = {
        ["x86_64-w64-mingw32-g++"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]]
    },
    find_programver = {
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++"] = "15.1.0"
    }
}